<route lang="yaml">
meta:
  title: 示例页面
  icon: i-ep:document
</route>

<script setup lang="ts">
defineOptions({
  name: 'Example',
})
</script>

<template>
  <div class="example-container">
    <div class="example-card">
      <h2>示例页面</h2>
      <p>这是一个示例页面，你可以在这里添加你的业务逻辑。</p>
      <div class="example-content">
        <h3>功能特性</h3>
        <ul>
          <li>响应式布局</li>
          <li>侧边栏导航</li>
          <li>顶部导航栏</li>
          <li>路由管理</li>
          <li>状态管理</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<style scoped>
.example-container {
  padding: 20px;
}

.example-card {
  background: var(--g-container-bg);
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.example-card h2 {
  margin-bottom: 16px;
  font-size: 20px;
  color: var(--g-text-color);
}

.example-card p {
  margin-bottom: 20px;
  color: var(--g-text-color-secondary);
  line-height: 1.6;
}

.example-content h3 {
  margin-bottom: 12px;
  font-size: 16px;
  color: var(--g-text-color);
}

.example-content ul {
  margin: 0;
  padding-left: 20px;
}

.example-content li {
  margin-bottom: 8px;
  color: var(--g-text-color-secondary);
  line-height: 1.5;
}
</style>
