import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/system',
  component: Layout,
  name: 'systemManage',
  meta: {
    title: '系统管理',
    icon: 'i-ep:setting',
  },
  children: [
    {
      path: 'prem',
      name: 'systemManageMenu',
      component: () => import('@/views/system/prem.vue'),
      meta: {
        title: '权限管理',
        icon: 'i-ep:lock',
      },
    },
  ],
}

export default routes



