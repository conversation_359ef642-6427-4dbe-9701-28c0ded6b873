<script setup lang="ts">
import eventBus from '@/utils/eventBus'

defineOptions({
  name: 'HotkeysIntro',
})

const isShow = ref(false)

const settingsStore = useSettingsStore()

onMounted(() => {
  eventBus.on('global-hotkeys-intro-toggle', () => {
    isShow.value = !isShow.value
  })
})
</script>

<template>
  <DxModal v-model="isShow" title="快捷键介绍" :footer="false">
    <div class="px-4">
      <div class="grid gap-4 sm-grid-cols-2">
        <div>
          <h2 class="m-0 text-lg font-bold">
            全局
          </h2>
          <ul class="list-none ps-2 pt-2 text-sm">
            <li class="flex-baseline gap-2 py-1">
              <div class="flex-shrink-0 space-x-1">
                <DxKbd>{{ settingsStore.os === 'mac' ? '⌘' : 'Ctrl' }}</DxKbd>
                <DxKbd>I</DxKbd>
              </div>
              查看系统信息
            </li>
            <li v-if="settingsStore.settings.toolbar.navSearch && settingsStore.settings.navSearch.enableHotkeys" class="flex-baseline gap-2 py-1">
              <div class="flex-shrink-0 space-x-1">
                <DxKbd>{{ settingsStore.os === 'mac' ? '⌘' : 'Ctrl' }}</DxKbd>
                <DxKbd>K</DxKbd>
              </div>
              唤起导航搜索
            </li>
          </ul>
        </div>
        <div v-if="settingsStore.settings.menu.enableHotkeys && ['side', 'head'].includes(settingsStore.settings.menu.mode)">
          <h2 class="m-0 text-lg font-bold">
            主导航
          </h2>
          <ul class="list-none ps-2 pt-2 text-sm">
            <li class="flex-baseline gap-2 py-1">
              <div class="flex-shrink-0 space-x-1">
                <DxKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</DxKbd>
                <DxKbd>`</DxKbd>
              </div>
              激活下一个主导航
            </li>
            <li class="flex-baseline gap-2 py-1">
              <div class="flex-shrink-0 space-x-1">
                <DxKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</DxKbd>
                <DxKbd>{{ settingsStore.os === 'mac' ? '⇧' : 'Shift' }}</DxKbd>
                <DxKbd>`</DxKbd>
              </div>
              激活上一个主导航
            </li>
          </ul>
        </div>
        <div v-if="settingsStore.settings.tabbar.enable && settingsStore.settings.tabbar.enableHotkeys">
          <h2 class="m-0 text-lg font-bold">
            标签栏
          </h2>
          <ul class="list-none ps-2 pt-2 text-sm">
            <li class="flex-baseline gap-2 py-1">
              <div class="flex-shrink-0 space-x-1">
                <DxKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</DxKbd>
                <DxKbd>←</DxKbd>
              </div>
              切换到上一个标签页
            </li>
            <li class="flex-baseline gap-2 py-1">
              <div class="flex-shrink-0 space-x-1">
                <DxKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</DxKbd>
                <DxKbd>→</DxKbd>
              </div>
              切换到下一个标签页
            </li>
            <li class="flex-baseline gap-2 py-1">
              <div class="flex-shrink-0 space-x-1">
                <DxKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</DxKbd>
                <DxKbd>W</DxKbd>
              </div>
              关闭当前标签页
            </li>
            <li class="flex-baseline gap-2 py-1">
              <div class="flex-shrink-0 space-x-1">
                <DxKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</DxKbd>
                <DxKbd>1~9</DxKbd>
              </div>
              切换到第 n 个标签页
            </li>
            <li class="flex-baseline gap-2 py-1">
              <div class="flex-shrink-0 space-x-1">
                <DxKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</DxKbd>
                <DxKbd>0</DxKbd>
              </div>
              切换到最后一个标签页
            </li>
          </ul>
        </div>
      </div>
    </div>
  </DxModal>
</template>
