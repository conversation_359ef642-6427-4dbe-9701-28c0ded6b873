{"type": "module", "version": "5.6.0", "packageManager": "pnpm@10.14.0", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build:test": "vue-tsc -b && vite build --mode test", "serve": "http-server ./dist -o", "serve:test": "http-server ./dist-test -o", "svgo": "svgo -f src/assets/icons", "generate:icons": "tsx ./scripts/generate.icons.ts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc -b", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "preinstall": "npx only-allow pnpm", "taze": "taze minor -wIr", "commit": "git cz", "release": "bumpp"}, "dependencies": {"@vee-validate/zod": "^4.15.1", "@vueuse/components": "^13.6.0", "@vueuse/core": "^13.6.0", "@vueuse/integrations": "^13.6.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "defu": "^6.1.4", "disable-devtool": "^0.3.9", "element-plus": "^2.10.6", "eruda": "^3.4.3", "es-toolkit": "^1.39.8", "filesize": "^11.0.2", "hotkeys-js": "^3.13.15", "lucide-vue-next": "^0.539.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.3", "qs": "^6.14.0", "reka-ui": "^2.4.1", "scule": "^1.3.0", "tailwind-merge": "^3.3.1", "ua-parser-js": "^2.0.4", "vconsole": "^3.15.1", "vee-validate": "^4.15.1", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue-sonner": "^2.0.2", "zod": "^3.24.1"}, "devDependencies": {"@antfu/eslint-config": "^5.2.0", "@clack/prompts": "^0.11.0", "@faker-js/faker": "^9.9.0", "@iconify/json": "^2.2.367", "@iconify/vue": "^5.0.0", "@stylistic/stylelint-config": "^3.0.1", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.14.0", "@unocss/eslint-plugin": "^66.4.2", "@unocss/preset-legacy-compat": "^66.4.2", "@vitejs/plugin-legacy": "^7.2.1", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "boxen": "^8.0.1", "bumpp": "^10.2.2", "cz-git": "^1.12.0", "eslint": "^9.33.0", "fs-extra": "^11.3.1", "http-server": "^14.1.1", "lint-staged": "^16.1.5", "npm-run-all2": "^8.0.4", "picocolors": "^1.1.1", "plop": "^4.0.1", "postcss": "^8.5.6", "postcss-nested": "^7.0.2", "sass-embedded": "^1.90.0", "simple-git-hooks": "^2.13.1", "stylelint": "^16.23.1", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.12.1", "svgo": "^4.0.0", "taze": "^19.1.0", "tsx": "^4.20.3", "typescript": "^5.9.2", "unocss": "^66.4.2", "unocss-preset-animations": "^1.2.1", "unplugin-auto-import": "^20.0.0", "unplugin-turbo-console": "^2.2.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.1.1", "vite-plugin-app-loading": "^0.4.0", "vite-plugin-archiver": "^0.2.0", "vite-plugin-banner": "^0.8.1", "vite-plugin-compression2": "^2.2.0", "vite-plugin-env-parse": "^1.0.15", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-pages": "^0.33.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^8.0.0", "vite-plugin-vue-meta-layouts": "^0.5.1", "vue-tsc": "^3.0.5"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "preserveUnused": true}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}