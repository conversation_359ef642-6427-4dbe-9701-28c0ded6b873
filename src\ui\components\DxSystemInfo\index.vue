<script setup lang="ts">
import hotkeys from 'hotkeys-js'

defineOptions({
  name: 'DxSystemInfo',
})

const isShow = ref(false)

const { pkg, lastBuildTime } = __SYSTEM_INFO__

onMounted(() => {
  hotkeys('command+i, ctrl+i', () => {
    isShow.value = true
  })
})
</script>

<template>
  <DxDrawer v-model="isShow" title="系统信息" :footer="false">
    <div v-if="pkg.version">
      <DxDivider>
        系统信息
      </DxDivider>
      <div class="text-center text-lg font-bold font-sans">
        {{ pkg.version }}
      </div>
    </div>
    <div>
      <DxDivider>
        最后编译时间
      </DxDivider>
      <div class="text-center text-lg font-bold font-sans">
        {{ lastBuildTime }}
      </div>
    </div>
    <div>
      <DxDivider>
        生产环境依赖
      </DxDivider>
      <ul class="list-none text-sm">
        <li v-for="(val, key) in (pkg.dependencies as object)" :key="key" class="flex items-center justify-between rounded-lg px-2 py-1.5 hover-bg-secondary">
          <div class="font-bold">
            {{ key }}
          </div>
          <div class="font-sans">
            {{ val }}
          </div>
        </li>
      </ul>
    </div>
    <div>
      <DxDivider>
        开发环境依赖
      </DxDivider>
      <ul class="list-none text-sm">
        <li v-for="(val, key) in (pkg.devDependencies as object)" :key="key" class="flex items-center justify-between rounded-lg px-2 py-1.5 hover-bg-secondary">
          <div class="font-bold">
            {{ key }}
          </div>
          <div class="font-sans">
            {{ val }}
          </div>
        </li>
      </ul>
    </div>
  </DxDrawer>
</template>
