import router from '@/router'

export const useUserStore = defineStore(
  // 唯一ID
  'user',
  () => {
    const settingsStore = useSettingsStore()
    const menuStore = useMenuStore()

    const account = ref(localStorage.account ?? '')
    const token = ref(localStorage.token ?? '')
    const avatar = ref(localStorage.avatar ?? '')
    const permissions = ref<string[]>([])
    const isLogin = computed(() => {
      if (token.value) {
        return true
      }
      return false
    })

    // 登录 - 简化版本，实际项目中需要调用真实API
    async function login(data: {
      account: string
      password: string
    }) {
      // 模拟登录，实际项目中应该调用真实的API
      const mockRes = {
        data: {
          account: data.account,
          token: 'mock-token-' + Date.now(),
          avatar: ''
        }
      }
      localStorage.setItem('account', mockRes.data.account)
      localStorage.setItem('token', mockRes.data.token)
      localStorage.setItem('avatar', mockRes.data.avatar)
      account.value = mockRes.data.account
      token.value = mockRes.data.token
      avatar.value = mockRes.data.avatar
    }

    // 手动登出
    function logout(redirect = router.currentRoute.value.fullPath) {
      // 此处仅清除计算属性 isLogin 中判断登录状态过期的变量，以保证在弹出登录窗口模式下页面展示依旧正常
      localStorage.removeItem('token')
      token.value = ''
      router.push({
        name: 'login',
        query: {
          ...(redirect !== settingsStore.settings.home.fullPath && router.currentRoute.value.name !== 'login' && { redirect }),
        },
      }).then(logoutCleanStatus)
    }
    // 请求登出
    function requestLogout() {
      // 此处仅清除计算属性 isLogin 中判断登录状态过期的变量，以保证在弹出登录窗口模式下页面展示依旧正常
      localStorage.removeItem('token')
      token.value = ''
      router.push({
        name: 'login',
        query: {
          ...(
            router.currentRoute.value.fullPath !== settingsStore.settings.home.fullPath
            && router.currentRoute.value.name !== 'login'
            && {
              redirect: router.currentRoute.value.fullPath,
            }
          ),
        },
      }).then(logoutCleanStatus)
    }
    // 登出后清除状态
    function logoutCleanStatus() {
      localStorage.removeItem('account')
      localStorage.removeItem('avatar')
      account.value = ''
      avatar.value = ''
      permissions.value = []
      settingsStore.updateSettings({}, true)
      menuStore.setActived(0)
    }

    // 获取权限 - 简化版本
    async function getPermissions() {
      // 模拟权限获取，实际项目中应该调用真实的API
      permissions.value = ['read', 'write']
    }
    // 修改密码 - 简化版本
    async function editPassword(data: {
      password: string
      newPassword: string
    }) {
      // 模拟密码修改，实际项目中应该调用真实的API
      console.log('Password changed successfully')
    }

    return {
      account,
      token,
      avatar,
      permissions,
      isLogin,
      login,
      logout,
      requestLogout,
      getPermissions,
      editPassword,
    }
  },
)
