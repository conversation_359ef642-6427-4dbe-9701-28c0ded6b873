/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccountButton: typeof import('./../components/AccountButton/index.vue')['default']
    DxAuth: typeof import('./../ui/components/DxAuth/index.vue')['default']
    DxAvatar: typeof import('./../ui/components/DxAvatar/index.vue')['default']
    DxBackToTop: typeof import('./../ui/components/DxBackToTop/index.vue')['default']
    DxButton: typeof import('./../ui/components/DxButton/index.vue')['default']
    DxButtonGroup: typeof import('./../ui/components/DxButtonGroup/index.vue')['default']
    DxCard: typeof import('./../ui/components/DxCard/index.vue')['default']
    DxCheckbox: typeof import('./../ui/components/DxCheckbox/index.vue')['default']
    DxContextMenu: typeof import('./../ui/components/DxContextMenu/index.vue')['default']
    DxCopyright: typeof import('./../ui/components/DxCopyright/index.vue')['default']
    DxDivider: typeof import('./../ui/components/DxDivider/index.vue')['default']
    DxDrawer: typeof import('./../ui/components/DxDrawer/index.vue')['default']
    DxDropdown: typeof import('./../ui/components/DxDropdown/index.vue')['default']
    DxFileUpload: typeof import('./../ui/components/DxFileUpload/index.vue')['default']
    DxFixedActionBar: typeof import('./../ui/components/DxFixedActionBar/index.vue')['default']
    DxHoverCard: typeof import('./../ui/components/DxHoverCard/index.vue')['default']
    DxIcon: typeof import('./../ui/components/DxIcon/index.vue')['default']
    DxImagePreview: typeof import('./../ui/components/DxImagePreview/index.vue')['default']
    DxImageUpload: typeof import('./../ui/components/DxImageUpload/index.vue')['default']
    DxInput: typeof import('./../ui/components/DxInput/index.vue')['default']
    DxKbd: typeof import('./../ui/components/DxKbd/index.vue')['default']
    DxModal: typeof import('./../ui/components/DxModal/index.vue')['default']
    DxNotAllowed: typeof import('./../ui/components/DxNotAllowed/index.vue')['default']
    DxNotification: typeof import('./../ui/components/DxNotification/index.vue')['default']
    DxPageHeader: typeof import('./../ui/components/DxPageHeader/index.vue')['default']
    DxPageMain: typeof import('./../ui/components/DxPageMain/index.vue')['default']
    DxPasswordStrength: typeof import('./../ui/components/DxPasswordStrength/index.vue')['default']
    DxPinInput: typeof import('./../ui/components/DxPinInput/index.vue')['default']
    DxPopover: typeof import('./../ui/components/DxPopover/index.vue')['default']
    DxProgress: typeof import('./../ui/components/DxProgress/index.vue')['default']
    DxScrollArea: typeof import('./../ui/components/DxScrollArea/index.vue')['default']
    DxSearchBar: typeof import('./../ui/components/DxSearchBar/index.vue')['default']
    DxSelect: typeof import('./../ui/components/DxSelect/index.vue')['default']
    DxSlider: typeof import('./../ui/components/DxSlider/index.vue')['default']
    DxSmartFixedBlock: typeof import('./../ui/components/DxSmartFixedBlock/index.vue')['default']
    DxSwitch: typeof import('./../ui/components/DxSwitch/index.vue')['default']
    DxSystemInfo: typeof import('./../ui/components/DxSystemInfo/index.vue')['default']
    DxTabs: typeof import('./../ui/components/DxTabs/index.vue')['default']
    DxTextarea: typeof import('./../ui/components/DxTextarea/index.vue')['default']
    DxToast: typeof import('./../ui/components/DxToast/index.vue')['default']
    DxTooltip: typeof import('./../ui/components/DxTooltip/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
