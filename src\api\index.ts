import axios from 'axios'

const api = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASEURL || '/api',
  timeout: 1000 * 60,
  responseType: 'json',
})

api.interceptors.request.use(
  (request) => {
    const userStore = useUserStore()
    // 设置请求头
    if (request.headers && userStore.isLogin) {
      request.headers.Authorization = `Bearer ${userStore.token}`
    }
    return request
  },
)

api.interceptors.response.use(
  (response) => {
    // 简化的响应处理
    return Promise.resolve(response.data)
  },
  (error) => {
    let message = error.message
    if (message === 'Network Error') {
      message = '网络连接失败'
    }
    else if (message.includes('timeout')) {
      message = '请求超时'
    }
    else if (message.includes('Request failed with status code')) {
      message = `请求失败: ${error.response?.status}`
    }
    console.error('API Error:', message)
    return Promise.reject(error)
  },
)

export default api
