<script setup lang="ts">
  defineOptions({
    name: 'Topbar'
  });

  const settingsStore = useSettingsStore();
</script>

<template>
  <div class="topbar-container">
    <!-- 简化的顶部栏，可以在这里添加面包屑或其他简单工具 -->
  </div>
</template>

<style scoped>
  .topbar-container {
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: var(--g-main-area-bg);
    transition: background-color 0.3s;
    min-height: 0;
  }
</style>
