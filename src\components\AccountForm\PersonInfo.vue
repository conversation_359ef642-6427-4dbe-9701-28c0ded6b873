<script setup lang="ts">

// import { deliverWithdraw, getUserDetail } from '@/api/realName/information';
// import { getToken } from '@/utils/auth';
// import { codeApi, userCodeApi } from '@/api/realName/realName';

// ---------------- 类型定义 ----------------
interface UserInfo {
  realName: string;
  idCard: string;
  bankCard: string;
  mobile: string;
  balance: number | string;
  courseBalance: number | string;
}

// ---------------- 状态 ----------------
const dialogTableVisible = ref(false);
const inforList = ref<UserInfo | null>(null);
const amount = ref<string | number>('');
const signContractStatus = ref<number>(0);
const sendmsg = ref('发送验证码');
const isSend = ref(true);

// ---------------- 方法 ----------------
const toAuthenFn = () => {
  const req = 'token=' + getToken();
  const encode = Base64.encode(Base64.encode(req));

  if (window.location.host.startsWith('deliver')) {
    window.open(`https://pay.dxznjy.com/pay/account?${encode}`, '_blank');
  } else if (window.location.host.startsWith('test-deliver')) {
    window.open(`https://test-pay.dxznjy.com/pay/account?${encode}`, '_blank');
  } else if (/^[0-9.]+:[0-9]+$/.test(window.location.host)) {
    window.open(`http://*************:8000/pay/account?${encode}`, '_blank');
  } else {
    const a = window.location.host.split('.')[0].slice(0, -1);
    const b = `https://${a}i.dxznjy.com/`;
    window.open(b + 'pay/account?' + encode, '_blank');
  }
};

const initData = async () => {
  const codeLin = await codeApi();
  const codecanshu = codeLin.data.find((item: any) => item.userType === 'Member');
  const res = await userCodeApi(codecanshu.userCode);
  signContractStatus.value = res.data.signContractStatus;

  const { data } = await getUserDetail();
  inforList.value = data;
};

const btnOk = async () => {
  dialogTableVisible.value = false;
  await deliverWithdraw(amount.value);
};

const toWithdraw = () => {
  const req = 'token=' + getToken();
  const encode = Base64.encode(Base64.encode(req));

  if (window.location.host.startsWith('deliver')) {
    window.open(`https://pay.dxznjy.com/pay/account?${encode}`, '_blank');
  } else if (window.location.host.startsWith('test-deliver')) {
    window.open(`https://test-pay.dxznjy.com/pay/account?${encode}`, '_blank');
  } else if (window.location.host.startsWith('uat-deliver')) {
    window.open(`https://uat-dxpay.ngrok.dxznjy.com/pay/account?${encode}`, '_blank');
  } else if (/^[0-9.]+:[0-9]+$/.test(window.location.host)) {
    window.open(`http://*************:8000/pay/account?${encode}`, '_blank');
  } else {
    const a = window.location.host.split('.')[0].slice(0, -1);
    const b = `https://${a}i.dxznjy.com/`;
    window.open(b + 'pay/account?' + encode, '_blank');
  }
};

// ---------------- 生命周期 ----------------
onMounted(() => {
  // initData();
});
</script>

<template>
  <div class="person-info-container">
    <!-- 个人信息卡片 -->
    <div class="info-card">
      <div class="card-header">
        <DxIcon name="i-mdi:account-circle" class="header-icon" />
        <h2 class="card-title">个人信息</h2>
      </div>
      
      <div class="info-grid">
        <!-- 姓名 -->
        <div class="info-item">
          <div class="info-label">
            <DxIcon name="i-mdi:account" class="label-icon" />
            <span>姓名</span>
          </div>
          <div class="info-value">{{ inforList?.realName || '未设置' }}</div>
        </div>

        <!-- 身份证号 -->
        <div class="info-item">
          <div class="info-label">
            <DxIcon name="i-mdi:card-account-details" class="label-icon" />
            <span>身份证号</span>
          </div>
          <div class="info-value">{{ inforList?.idCard || '未设置' }}</div>
        </div>

        <!-- 银行卡号 -->
        <div class="info-item">
          <div class="info-label">
            <DxIcon name="i-mdi:credit-card" class="label-icon" />
            <span>银行卡号</span>
          </div>
          <div class="info-value">{{ inforList?.bankCard || '未设置' }}</div>
        </div>

        <!-- 手机号 -->
        <div class="info-item">
          <div class="info-label">
            <DxIcon name="i-mdi:phone" class="label-icon" />
            <span>手机号</span>
          </div>
          <div class="info-value">{{ inforList?.mobile || '未设置' }}</div>
        </div>

        <!-- 实名认证 -->
        <div class="info-item">
          <div class="info-label">
            <DxIcon name="i-mdi:shield-check" class="label-icon" />
            <span>实名认证</span>
          </div>
          <div class="info-value">
            <div class="status-badge" :class="{ 'verified': signContractStatus === 1, 'unverified': signContractStatus !== 1 }">
              <DxIcon :name="signContractStatus === 1 ? 'i-mdi:check-circle' : 'i-mdi:alert-circle'" class="status-icon" />
              {{ signContractStatus === 1 ? '已认证' : '未认证' }}
            </div>
            <DxButton 
              v-if="signContractStatus !== 1" 
              variant="default" 
              size="sm" 
              class="action-btn"
              @click="toAuthenFn"
            >
              <DxIcon name="i-mdi:account-check" class="btn-icon" />
              去认证
            </DxButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 余额信息卡片 -->
    <div class="balance-card">
      <div class="card-header">
        <DxIcon name="i-mdi:wallet" class="header-icon" />
        <h2 class="card-title">账户余额</h2>
      </div>
      
      <div class="balance-grid">
        <!-- 当前余额 -->
        <div class="balance-item">
          <div class="balance-label">
            <DxIcon name="i-mdi:cash" class="balance-icon" />
            <span>当前余额</span>
          </div>
          <div class="balance-amount">¥{{ inforList?.balance || '0.00' }}</div>
          <DxButton 
            variant="default" 
            size="sm" 
            class="withdraw-btn"
            @click="toWithdraw"
          >
            <DxIcon name="i-mdi:cash-multiple" class="btn-icon" />
            提取
          </DxButton>
        </div>

        <!-- 课程余额 -->
        <div class="balance-item">
          <div class="balance-label">
            <DxIcon name="i-mdi:book-education" class="balance-icon" />
            <span>课程余额</span>
          </div>
          <div class="balance-amount course-balance">¥{{ inforList?.courseBalance || '0.00' }}</div>
        </div>
      </div>
    </div>

    <!-- 提取对话框 -->
    <DxModal 
      v-model="dialogTableVisible" 
      title="余额提取" 
      class="withdraw-modal"
      :header="true"
      :footer="false"
    >
      <div class="withdraw-content">
        <div class="current-balance">
          <DxIcon name="i-mdi:cash" class="balance-modal-icon" />
          <span class="balance-label">当前余额：</span>
          <span class="balance-value">¥{{ inforList?.balance || '0.00' }}</span>
        </div>

        <div class="withdraw-input-group">
          <DxIcon name="i-mdi:cash-multiple" class="input-icon" />
          <span class="input-label">提取金额：</span>
          <DxInput 
            v-model="amount" 
            placeholder="请输入提取金额"
            class="withdraw-input"
          />
        </div>
      </div>

      <template #footer>
        <div class="modal-footer">
          <DxButton 
            variant="outline" 
            @click="dialogTableVisible = false"
            class="cancel-btn"
          >
            取消
          </DxButton>
          <DxButton 
            variant="default" 
            @click="btnOk"
            class="confirm-btn"
          >
            确认提取
          </DxButton>
        </div>
      </template>
    </DxModal>
  </div>
</template>

<style lang="scss" scoped>
.person-info-container {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
  background: var(--g-main-area-bg);
  min-height: 100vh;
}

.info-card,
.balance-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid hsl(var(--border));

  .header-icon {
    font-size: 24px;
    color: hsl(var(--primary));
  }

  .card-title {
    font-size: 20px;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin: 0;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: hsl(var(--muted) / 0.3);
  border-radius: 8px;
  border: 1px solid hsl(var(--border));

  .info-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: hsl(var(--muted-foreground));

    .label-icon {
      font-size: 16px;
      color: hsl(var(--primary));
    }
  }

  .info-value {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    font-weight: 500;
    color: hsl(var(--foreground));
    min-height: 24px;
  }
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;

  &.verified {
    background: hsl(var(--success) / 0.1);
    color: hsl(var(--success));
    border: 1px solid hsl(var(--success) / 0.2);
  }

  &.unverified {
    background: hsl(var(--warning) / 0.1);
    color: hsl(var(--warning));
    border: 1px solid hsl(var(--warning) / 0.2);
  }

  .status-icon {
    font-size: 16px;
  }
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.2s ease;

  .btn-icon {
    font-size: 14px;
  }
}

.balance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.balance-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.05) 0%, hsl(var(--primary) / 0.02) 100%);
  border: 1px solid hsl(var(--primary) / 0.1);
  border-radius: 12px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary) / 0.7));
  }
}

.balance-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: hsl(var(--muted-foreground));

  .balance-icon {
    font-size: 18px;
    color: hsl(var(--primary));
  }
}

.balance-amount {
  font-size: 28px;
  font-weight: 700;
  color: hsl(var(--foreground));
  line-height: 1.2;

  &.course-balance {
    color: hsl(var(--secondary-foreground));
  }
}

.withdraw-btn {
  align-self: flex-start;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;

  .btn-icon {
    font-size: 14px;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px hsl(var(--primary) / 0.3);
  }
}

// 模态框样式
.withdraw-modal {
  :deep(.fa-modal-content) {
    max-width: 500px;
    width: 90vw;
  }
}

.withdraw-content {
  padding: 24px 0;
}

.current-balance {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: hsl(var(--muted) / 0.3);
  border-radius: 8px;
  margin-bottom: 24px;

  .balance-modal-icon {
    font-size: 20px;
    color: hsl(var(--primary));
  }

  .balance-label {
    font-size: 16px;
    font-weight: 500;
    color: hsl(var(--muted-foreground));
  }

  .balance-value {
    font-size: 18px;
    font-weight: 600;
    color: hsl(var(--foreground));
  }
}

.withdraw-input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .input-icon {
    font-size: 18px;
    color: hsl(var(--primary));
  }

  .input-label {
    font-size: 16px;
    font-weight: 500;
    color: hsl(var(--foreground));
  }

  .withdraw-input {
    width: 100%;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid hsl(var(--border));

  .cancel-btn,
  .confirm-btn {
    min-width: 100px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .person-info-container {
    padding: 16px;
  }

  .info-grid,
  .balance-grid {
    grid-template-columns: 1fr;
  }

  .info-card,
  .balance-card {
    padding: 16px;
  }

  .balance-amount {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .person-info-container {
    padding: 12px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .info-item,
  .balance-item {
    padding: 12px;
  }
}
</style>
