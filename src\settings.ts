import type { RecursiveRequired, Settings } from '#/global'
import { cloneDeep } from 'es-toolkit'
import settingsDefault from '@/settings.default'
import { merge } from '@/utils/object'

const globalSettings: Settings.all = {
  "app": {
    "enableDynamicTitle": true
  },
  "home": {
    "title": "首页"
  },
  "menu": {
    "mode": "single",
    "mainMenuClickMode": "smart",
    "enableSubMenuCollapseButton": true
  },
  "tabbar": {
    "enable": true,
    "enableIcon": true,
    "enableHotkeys": true
  },
  "toolbar": {
    "pageReload": true
  }
}

export default merge(globalSettings, cloneDeep(settingsDefault)) as RecursiveRequired<Settings.all>
