<route lang="yaml">
meta:
  title: 主页
  icon: ant-design:home-twotone
</route>

<script setup lang="ts">
  defineOptions({
    name: 'Home'
  });
</script>

<template>
  <div class="home-container">
    <div class="welcome-card">
      <h1>欢迎使用管理系统</h1>
      <p>这是一个简洁的后台管理系统模板</p>
    </div>
  </div>
</template>

<style scoped>
  .home-container {
    padding: 20px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .welcome-card {
    text-align: center;
    padding: 40px;
    border-radius: 8px;
    background: var(--g-container-bg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .welcome-card h1 {
    margin-bottom: 16px;
    font-size: 24px;
    color: var(--g-text-color);
  }

  .welcome-card p {
    color: var(--g-text-color-secondary);
    font-size: 16px;
  }
</style>
