import type { Menu } from '#/global'

import MultilevelMenuExample from './modules/multilevel.menu.example'

const menu: Menu.recordMainRaw[] = [
  {
    meta: {
      title: '系统管理',
      icon: 'i-ep:setting',
    },
    children: [
      {
        path: '/system/permission/manage',
        name: 'permissionManage',
        meta: {
          title: '权限管理',
          icon: 'i-ep:lock',
        },
      },
    ],
  },
  {
    meta: {
      title: '演示',
      icon: 'uim:box',
    },
    children: [
      MultilevelMenuExample,
    ],
  },
]

export default menu
