# UI 组件使用文档

本文档介绍 `src/ui/components` 文件夹下所有全局组件的使用方法。

## 目录

按住CTtl+鼠标左键，可快速跳转到对应标题

- [基础组件](#基础组件)
- [表单组件](#表单组件)
- [布局组件](#布局组件)
- [反馈组件](#反馈组件)
- [导航组件](#导航组件)
- [数据展示](#数据展示)
- [其他组件](#其他组件)

## 基础组件

### DxButton - 按钮组件

基础按钮组件，支持多种样式和状态。

**Props:**

- `variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'` - 按钮样式
- `size?: 'default' | 'sm' | 'lg' | 'icon'` - 按钮尺寸
- `disabled?: boolean` - 是否禁用
- `loading?: boolean` - 是否显示加载状态
- `class?: string` - 自定义样式类

**使用示例:**

```vue
<template>
  <DxButton variant="default" size="default">默认按钮</DxButton>
  <DxButton variant="outline" :loading="true">加载中</DxButton>
  <DxButton variant="destructive" :disabled="true">禁用按钮</DxButton>
</template>
```

### DxButtonGroup - 按钮组

将多个按钮组合在一起的容器组件。

**Props:**

- `vertical?: boolean` - 是否垂直排列

**使用示例:**

```vue
<template>
  <DxButtonGroup>
    <DxButton>按钮1</DxButton>
    <DxButton>按钮2</DxButton>
    <DxButton>按钮3</DxButton>
  </DxButtonGroup>

  <DxButtonGroup vertical>
    <DxButton>垂直按钮1</DxButton>
    <DxButton>垂直按钮2</DxButton>
  </DxButtonGroup>
</template>
```

### DxIcon - 图标组件

支持多种图标类型的通用图标组件。

**Props:**

- `name: string` - 图标名称
- `class?: string` - 自定义样式类

**使用示例:**

```vue
<template>
  <DxIcon name="i-line-md:loading-loop" />
  <DxIcon name="i-tdesign:image-error" class="text-red-500" />
</template>
```

### DxAvatar - 头像组件

用于显示用户头像的组件。

**Props:**

- `src: string` - 头像图片地址
- `fallback?: string` - 备用文本
- `shape?: 'circle' | 'square'` - 头像形状
- `class?: string` - 自定义样式类

**Slots:**

- `default` - 自定义备用内容

**使用示例:**

```vue
<template>
  <DxAvatar src="/avatar.jpg" fallback="用户" />
  <DxAvatar src="/avatar.jpg" shape="square">
    <DxIcon name="i-user" />
  </DxAvatar>
</template>
```

## 表单组件

### DxInput - 输入框组件

基础输入框组件，支持双向绑定。

**Props:**

- `disabled?: boolean` - 是否禁用
- `class?: string` - 自定义样式类

**v-model:**

- `modelValue: string | number` - 输入值

**使用示例:**

```vue
<script setup>
  const inputValue = ref('');
</script>

<template>
  <DxInput v-model="inputValue" placeholder="请输入内容" />
  <DxInput v-model="inputValue" :disabled="true" />
</template>
```

### DxTextarea - 文本域组件

多行文本输入组件。

**使用示例:**

```vue
<script setup>
  const textValue = ref('');
</script>

<template>
  <DxTextarea v-model="textValue" placeholder="请输入多行文本" />
</template>
```

### DxSelect - 选择器组件

下拉选择组件。

**Props:**

- `disabled?: boolean` - 是否禁用
- `options: Array<{label: string, value: string, disabled?: boolean}>` - 选项数据
- `class?: string` - 自定义样式类

**v-model:**

- `modelValue: string` - 选中值

**使用示例:**

```vue
<script setup>
  const selectedValue = ref('');
  const options = [
    { label: '选项1', value: '1' },
    { label: '选项2', value: '2' },
    { label: '选项3', value: '3', disabled: true }
  ];
</script>

<template>
  <DxSelect v-model="selectedValue" :options="options" />
</template>
```

### DxCheckbox - 复选框组件

复选框输入组件。

**v-model:**

- `modelValue: boolean` - 选中状态

**使用示例:**

```vue
<script setup>
  const checked = ref(false);
</script>

<template>
  <DxCheckbox v-model="checked">同意条款</DxCheckbox>
</template>
```

### DxSwitch - 开关组件

开关切换组件。

**Props:**

- `disabled?: boolean` - 是否禁用
- `onIcon?: string` - 开启状态图标
- `offIcon?: string` - 关闭状态图标

**v-model:**

- `modelValue: boolean` - 开关状态

**使用示例:**

```vue
<script setup>
  const enabled = ref(false);
</script>

<template>
  <DxSwitch v-model="enabled" />
  <DxSwitch v-model="enabled" on-icon="i-check" off-icon="i-close" />
</template>
```

### DxSlider - 滑块组件

数值滑块输入组件。

**使用示例:**

```vue
<script setup>
  const sliderValue = ref(50);
</script>

<template>
  <DxSlider v-model="sliderValue" />
</template>
```

### DxPinInput - 验证码输入组件

用于输入验证码或PIN码的组件。

**使用示例:**

```vue
<script setup>
  const pinValue = ref('');
</script>

<template>
  <DxPinInput v-model="pinValue" />
</template>
```

### DxPasswordStrength - 密码强度组件

显示密码强度的组件。

**使用示例:**

```vue
<script setup>
  const password = ref('');
</script>

<template>
  <DxPasswordStrength :password="password" />
</template>
```

## 布局组件

### DxCard - 卡片组件

内容卡片容器组件。

**Props:**

- `title?: string` - 卡片标题
- `description?: string` - 卡片描述
- `class?: string` - 自定义样式类
- `headerClass?: string` - 头部样式类
- `contentClass?: string` - 内容样式类
- `footerClass?: string` - 底部样式类

**Slots:**

- `default` - 卡片内容
- `header` - 自定义头部
- `footer` - 自定义底部

**使用示例:**

```vue
<template>
  <DxCard title="卡片标题" description="卡片描述">
    <p>卡片内容</p>
    <template #footer>
      <DxButton>操作按钮</DxButton>
    </template>
  </DxCard>
</template>
```

### DxDivider - 分割线组件

内容分割线组件。

**使用示例:**

```vue
<template>
  <div>内容1</div>
  <DxDivider />
  <div>内容2</div>
</template>
```

### DxPageHeader - 页面头部组件

页面标题头部组件。

**Props:**

- `title?: string` - 页面标题
- `description?: string` - 页面描述
- `class?: string` - 自定义样式类
- `mainClass?: string` - 主要内容样式类
- `defaultClass?: string` - 默认内容样式类

**Slots:**

- `default` - 自定义内容
- `extra` - 额外操作区域

**使用示例:**

```vue
<template>
  <DxPageHeader title="页面标题" description="页面描述">
    <template #extra>
      <DxButton>新建</DxButton>
    </template>
  </DxPageHeader>
</template>
```

### DxPageMain - 页面主体组件

页面主要内容容器组件。

**使用示例:**

```vue
<template>
  <DxPageMain>
    <p>页面主要内容</p>
  </DxPageMain>
</template>
```

### DxScrollArea - 滚动区域组件

自定义滚动条的滚动容器。

**使用示例:**

```vue
<template>
  <DxScrollArea class="h-[200px]">
    <div class="p-4">
      <p v-for="i in 50" :key="i">滚动内容 {{ i }}</p>
    </div>
  </DxScrollArea>
</template>
```

### DxSmartFixedBlock - 智能固定块组件

智能定位的固定块组件。

**使用示例:**

```vue
<template>
  <DxSmartFixedBlock>
    <DxButton>固定按钮</DxButton>
  </DxSmartFixedBlock>
</template>
```

### DxFixedActionBar - 固定操作栏组件

固定在页面底部的操作栏。

**使用示例:**

```vue
<template>
  <DxFixedActionBar>
    <DxButton>保存</DxButton>
    <DxButton variant="outline">取消</DxButton>
  </DxFixedActionBar>
</template>
```

### DxBackToTop - 返回顶部组件

返回页面顶部的浮动按钮。

**使用示例:**

```vue
<template>
  <DxBackToTop />
</template>
```

## 反馈组件

### DxModal - 模态框组件

模态对话框组件，支持多种配置选项。

**Props:**

- `modelValue: boolean` - 显示状态
- `title?: string` - 标题
- `zIndex?: number` - 层级，默认2000
- `loading?: boolean` - 加载状态
- `closable?: boolean` - 是否可关闭，默认true
- `maximize?: boolean` - 是否最大化
- `maximizable?: boolean` - 是否可最大化
- `draggable?: boolean` - 是否可拖拽
- `center?: boolean` - 是否居中
- `border?: boolean` - 是否显示边框，默认true
- `overlay?: boolean` - 是否显示遮罩，默认true
- `overlayBlur?: boolean` - 遮罩是否模糊
- `showConfirmButton?: boolean` - 显示确认按钮，默认true
- `showCancelButton?: boolean` - 显示取消按钮，默认false
- `confirmButtonText?: string` - 确认按钮文本，默认'确定'
- `cancelButtonText?: string` - 取消按钮文本，默认'取消'
- `confirmButtonDisabled?: boolean` - 确认按钮禁用状态
- `confirmButtonLoading?: boolean` - 确认按钮加载状态
- `header?: boolean` - 显示头部，默认true
- `footer?: boolean` - 显示底部，默认true
- `closeOnClickOverlay?: boolean` - 点击遮罩关闭，默认true
- `closeOnPressEscape?: boolean` - 按ESC关闭，默认true
- `destroyOnClose?: boolean` - 关闭时销毁，默认true

**Events:**

- `confirm` - 确认事件
- `cancel` - 取消事件
- `close` - 关闭事件

**Slots:**

- `header` - 自定义头部
- `default` - 主要内容
- `footer` - 自定义底部

**使用示例:**

```vue
<script setup>
  const showModal = ref(false);

  function handleConfirm() {
    console.log('确认');
    showModal.value = false;
  }
</script>

<template>
  <DxButton @click="showModal = true">打开模态框</DxButton>

  <DxModal v-model="showModal" title="确认操作" @confirm="handleConfirm">
    <p>确定要执行此操作吗？</p>
  </DxModal>
</template>
```

### DxDrawer - 抽屉组件

侧边抽屉组件，支持多个方向。

**Props:**

- `modelValue: boolean` - 显示状态
- `title?: string` - 标题
- `side?: 'top' | 'right' | 'bottom' | 'left'` - 抽屉方向，默认'right'
- `zIndex?: number` - 层级，默认2000
- `loading?: boolean` - 加载状态
- `closable?: boolean` - 是否可关闭，默认true
- `centered?: boolean` - 是否居中
- `bordered?: boolean` - 是否显示边框，默认true
- `overlay?: boolean` - 是否显示遮罩，默认true
- `overlayBlur?: boolean` - 遮罩是否模糊
- `showConfirmButton?: boolean` - 显示确认按钮，默认true
- `showCancelButton?: boolean` - 显示取消按钮，默认false
- `confirmButtonText?: string` - 确认按钮文本
- `cancelButtonText?: string` - 取消按钮文本
- `header?: boolean` - 显示头部，默认true
- `footer?: boolean` - 显示底部，默认true
- `closeOnClickOverlay?: boolean` - 点击遮罩关闭，默认true
- `closeOnPressEscape?: boolean` - 按ESC关闭，默认true
- `destroyOnClose?: boolean` - 关闭时销毁，默认true

**使用示例:**

```vue
<script setup>
  const showDrawer = ref(false);
</script>

<template>
  <DxButton @click="showDrawer = true">打开抽屉</DxButton>

  <DxDrawer v-model="showDrawer" title="抽屉标题" side="right">
    <p>抽屉内容</p>
  </DxDrawer>
</template>
```

### DxNotification - 通知组件

消息通知组件。

**使用示例:**

```vue
<template>
  <DxNotification />
</template>
```

### DxToast - 轻提示组件

轻量级提示组件。

**使用示例:**

```vue
<template>
  <DxToast />
</template>
```

### DxProgress - 进度条组件

进度显示组件。

**使用示例:**

```vue
<script setup>
  const progress = ref(60);
</script>

<template>
  <DxProgress :value="progress" />
</template>
```

### DxTooltip - 工具提示组件

鼠标悬停提示组件。

**Props:**

- `text?: string` - 提示文本
- `delay?: number` - 延迟显示时间，默认300ms
- `side?: 'top' | 'right' | 'bottom' | 'left'` - 显示位置
- `align?: 'start' | 'center' | 'end'` - 对齐方式
- `disabled?: boolean` - 是否禁用

**Slots:**

- `default` - 触发元素
- `content` - 自定义提示内容

**使用示例:**

```vue
<template>
  <DxTooltip text="这是提示信息">
    <DxButton>悬停查看提示</DxButton>
  </DxTooltip>

  <DxTooltip side="top">
    <DxButton>自定义提示</DxButton>
    <template #content>
      <div class="p-2">
        <h4>自定义标题</h4>
        <p>自定义内容</p>
      </div>
    </template>
  </DxTooltip>
</template>
```

### DxPopover - 弹出框组件

点击弹出的内容框组件。

**Props:**

- `align?: 'start' | 'center' | 'end'` - 对齐方式
- `alignOffset?: number` - 对齐偏移
- `side?: 'top' | 'right' | 'bottom' | 'left'` - 显示位置
- `sideOffset?: number` - 位置偏移
- `collisionPadding?: number` - 碰撞边距
- `class?: string` - 自定义样式类

**v-model:**

- `open: boolean` - 显示状态

**Slots:**

- `trigger` - 触发元素
- `default` - 弹出内容

**使用示例:**

```vue
<script setup>
  const showPopover = ref(false);
</script>

<template>
  <DxPopover v-model:open="showPopover">
    <template #trigger>
      <DxButton>点击弹出</DxButton>
    </template>
    <div class="p-4">
      <h4>弹出内容</h4>
      <p>这里是弹出框的内容</p>
    </div>
  </DxPopover>
</template>
```

### DxHoverCard - 悬停卡片组件

鼠标悬停显示的卡片组件。

**Slots:**

- `trigger` - 触发元素
- `default` - 卡片内容

**使用示例:**

```vue
<template>
  <DxHoverCard>
    <template #trigger>
      <DxButton>悬停显示卡片</DxButton>
    </template>
    <div class="p-4">
      <h4>卡片标题</h4>
      <p>卡片详细内容</p>
    </div>
  </DxHoverCard>
</template>
```

## 导航组件

### DxTabs - 标签页组件

标签页切换组件。

**Props:**

- `modelValue?: string | number` - 当前激活的标签值
- `list: Array<{icon?: string, label: string, value: string | number, class?: string}>` - 标签页数据
- `class?: string` - 自定义样式类
- `listClass?: string` - 标签列表样式类
- `contentClass?: string` - 内容区域样式类

**Events:**

- `update:modelValue` - 标签切换事件

**Slots:**

- 动态插槽，以标签的value作为插槽名

**使用示例:**

```vue
<script setup>
  const activeTab = ref('tab1');
  const tabList = [
    { label: '标签1', value: 'tab1', icon: 'i-home' },
    { label: '标签2', value: 'tab2', icon: 'i-user' },
    { label: '标签3', value: 'tab3', icon: 'i-settings' }
  ];
</script>

<template>
  <DxTabs v-model="activeTab" :list="tabList">
    <template #tab1>
      <div>标签1的内容</div>
    </template>
    <template #tab2>
      <div>标签2的内容</div>
    </template>
    <template #tab3>
      <div>标签3的内容</div>
    </template>
  </DxTabs>
</template>
```

### DxContextMenu - 右键菜单组件

右键上下文菜单组件。

**Props:**

- `items: Array<Array<{label: string, icon?: string, disabled?: boolean, hide?: boolean, handle?: () => void}>>` - 菜单项数据，二维数组表示分组

**Slots:**

- `default` - 触发右键菜单的元素
- `label` - 自定义菜单标签

**使用示例:**

```vue
<script setup>
  const menuItems = [
    [
      { label: '复制', icon: 'i-copy', handle: () => console.log('复制') },
      { label: '粘贴', icon: 'i-paste', handle: () => console.log('粘贴') }
    ],
    [{ label: '删除', icon: 'i-delete', handle: () => console.log('删除') }]
  ];
</script>

<template>
  <DxContextMenu :items="menuItems">
    <div class="p-4 border">右键点击此区域</div>
  </DxContextMenu>
</template>
```

### DxDropdown - 下拉菜单组件

下拉菜单组件。

**Slots:**

- `trigger` - 触发元素
- `default` - 菜单内容

**使用示例:**

```vue
<template>
  <DxDropdown>
    <template #trigger>
      <DxButton>下拉菜单</DxButton>
    </template>
    <div class="p-2">
      <div class="menu-item">菜单项1</div>
      <div class="menu-item">菜单项2</div>
      <div class="menu-item">菜单项3</div>
    </div>
  </DxDropdown>
</template>
```

## 数据展示

### DxImagePreview - 图片预览组件

图片预览和放大组件。

**使用示例:**

```vue
<script setup>
  const images = ['/image1.jpg', '/image2.jpg', '/image3.jpg'];
</script>

<template>
  <DxImagePreview :images="images" />
</template>
```

### DxKbd - 键盘按键组件

显示键盘按键的组件。

**使用示例:**

```vue
<template>
  <p>
    按
    <DxKbd>Ctrl</DxKbd>
    +
    <DxKbd>C</DxKbd>
    复制
  </p>
  <p>
    按
    <DxKbd>Enter</DxKbd>
    确认
  </p>
</template>
```

### DxSystemInfo - 系统信息组件

显示系统信息的组件。

**使用示例:**

```vue
<template>
  <DxSystemInfo />
</template>
```

### DxCopyright - 版权信息组件

显示版权信息的组件。

**使用示例:**

```vue
<template>
  <DxCopyright />
</template>
```

## 其他组件

### DxAuth - 权限控制组件

基于权限控制显示内容的组件。

**Props:**

- `value: string | string[]` - 权限值或权限数组
- `all?: boolean` - 是否需要全部权限，默认false

**Slots:**

- `default` - 有权限时显示的内容
- `no-auth` - 无权限时显示的内容

**使用示例:**

```vue
<template>
  <DxAuth value="admin">
    <DxButton>管理员操作</DxButton>
    <template #no-auth>
      <span class="text-gray-400">无权限</span>
    </template>
  </DxAuth>

  <DxAuth :value="['read', 'write']" all>
    <DxButton>需要读写权限</DxButton>
  </DxAuth>
</template>
```

### DxSearchBar - 搜索栏组件

搜索输入组件。

**使用示例:**

```vue
<script setup>
  const searchValue = ref('');

  function handleSearch() {
    console.log('搜索:', searchValue.value);
  }
</script>

<template>
  <DxSearchBar v-model="searchValue" @search="handleSearch" />
</template>
```

### DxFileUpload - 文件上传组件

文件上传组件。

**使用示例:**

```vue
<script setup>
  function handleUpload(files) {
    console.log('上传文件:', files);
  }
</script>

<template>
  <DxFileUpload @upload="handleUpload" />
</template>
```

### DxImageUpload - 图片上传组件

专门用于图片上传的组件。

**使用示例:**

```vue
<script setup>
  const imageList = ref([]);

  function handleImageUpload(images) {
    imageList.value = images;
  }
</script>

<template>
  <DxImageUpload v-model="imageList" @upload="handleImageUpload" />
</template>
```

### DxNotAllowed - 无权限提示组件

显示无权限访问提示的组件。

**使用示例:**

```vue
<template>
  <DxNotAllowed />
</template>
```

## 使用注意事项

1. **全局注册**: 所有组件都已全局注册，可以在任何Vue组件中直接使用，无需手动导入。

2. **样式定制**: 大部分组件支持通过 `class` 属性传入自定义样式类进行样式定制。

3. **响应式**: 组件内部使用了响应式设计，在不同屏幕尺寸下都有良好的显示效果。

4. **无障碍**: 组件遵循无障碍设计原则，支持键盘导航和屏幕阅读器。

5. **主题**: 组件支持主题切换，会自动适应当前主题的颜色方案。

## 开发建议

1. **组合使用**: 可以将多个组件组合使用来构建复杂的UI界面。

2. **插槽定制**: 充分利用组件提供的插槽来定制内容和布局。

3. **事件处理**: 合理使用组件提供的事件来处理用户交互。

4. **性能优化**: 对于大量数据的组件，注意使用虚拟滚动等性能优化技术。

5. **类型安全**: 在TypeScript项目中，组件都提供了完整的类型定义，可以获得良好的类型提示和检查。
