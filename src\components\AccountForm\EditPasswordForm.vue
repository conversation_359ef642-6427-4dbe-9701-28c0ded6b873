<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/shadcn/ui/form'

defineOptions({
  name: 'EditPasswordForm',
})

const userStore = useUserStore()

const loading = ref(false)

const form = useForm({
  validationSchema: toTypedSchema(
    z.object({
      password: z.string().min(1, '请输入原密码'),
      newPassword: z.string().min(1, '请输入新密码').min(6, '密码长度为6到18位').max(18, '密码长度为6到18位'),
      checkPassword: z.string().min(1, '请确认新密码'),
    }).refine(data => data.newPassword === data.checkPassword, {
      message: '两次输入的密码不一致',
      path: ['checkPassword'],
    }),
  ),
  initialValues: {
    password: '',
    newPassword: '',
    checkPassword: '',
  },
})

const onSubmit = form.handleSubmit((values) => {
  loading.value = true
  userStore.editPassword(values).then(async () => {
    toast.success('模拟修改成功，请重新登录')
    userStore.logout()
  }).finally(() => {
    loading.value = false
  })
})
</script>

<template>
  <div class="edit-password-container">
    <!-- 主卡片 -->
    <div class="password-card">
      <!-- 头部区域 -->
      <div class="card-header">
        <div class="header-icon-wrapper">
          <DxIcon name="i-mdi:lock-reset" class="header-icon" />
        </div>
        <div class="header-content">
          <h2 class="card-title">修改密码</h2>
          <p class="card-subtitle">
            为了保障您的账户安全，请设置一个强密码
          </p>
        </div>
      </div>

      <!-- 表单区域 -->
      <form @submit="onSubmit" class="password-form">
        <!-- 原密码 -->
        <FormField v-slot="{ componentField, errors }" name="password">
          <FormItem class="form-item">
            <div class="input-wrapper">
              <div class="input-icon">
                <DxIcon name="i-mdi:lock" class="icon" />
              </div>
              <FormControl>
                <DxInput 
                  type="password" 
                  placeholder="请输入原密码" 
                  class="password-input"
                  :class="{ 'input-error': errors.length }"
                  v-bind="componentField" 
                />
              </FormControl>
            </div>
            <Transition 
              enter-active-class="transition-all duration-200 ease-out" 
              enter-from-class="opacity-0 -translate-y-1" 
              leave-active-class="transition-all duration-200 ease-in" 
              leave-to-class="opacity-0 -translate-y-1"
            >
              <FormMessage class="error-message" />
            </Transition>
          </FormItem>
        </FormField>

        <!-- 新密码 -->
        <FormField v-slot="{ componentField, errors }" name="newPassword">
          <FormItem class="form-item">
            <div class="input-wrapper">
              <div class="input-icon">
                <DxIcon name="i-mdi:lock-plus" class="icon" />
              </div>
              <FormControl>
                <DxInput 
                  type="password" 
                  placeholder="请输入新密码" 
                  class="password-input"
                  :class="{ 'input-error': errors.length }"
                  v-bind="componentField" 
                />
              </FormControl>
            </div>
            <Transition 
              enter-active-class="transition-all duration-200 ease-out" 
              enter-from-class="opacity-0 -translate-y-1" 
              leave-active-class="transition-all duration-200 ease-in" 
              leave-to-class="opacity-0 -translate-y-1"
            >
              <FormMessage class="error-message" />
            </Transition>
          </FormItem>
        </FormField>

        <!-- 确认密码 -->
        <FormField v-slot="{ componentField, errors }" name="checkPassword">
          <FormItem class="form-item">
            <div class="input-wrapper">
              <div class="input-icon">
                <DxIcon name="i-mdi:lock-check" class="icon" />
              </div>
              <FormControl>
                <DxInput 
                  type="password" 
                  placeholder="请确认新密码" 
                  class="password-input"
                  :class="{ 'input-error': errors.length }"
                  v-bind="componentField" 
                />
              </FormControl>
            </div>
            <Transition 
              enter-active-class="transition-all duration-200 ease-out" 
              enter-from-class="opacity-0 -translate-y-1" 
              leave-active-class="transition-all duration-200 ease-in" 
              leave-to-class="opacity-0 -translate-y-1"
            >
              <FormMessage class="error-message" />
            </Transition>
          </FormItem>
        </FormField>

        <!-- 密码强度提示 -->
        <div class="password-tips">
          <div class="tips-header">
            <DxIcon name="i-mdi:information" class="tips-icon" />
            <span class="tips-title">密码要求</span>
          </div>
          <ul class="tips-list">
            <li class="tip-item">
              <DxIcon name="i-mdi:check-circle" class="tip-icon valid" />
              <span>密码长度6-18位</span>
            </li>
            <li class="tip-item">
              <DxIcon name="i-mdi:check-circle" class="tip-icon valid" />
              <span>建议包含字母、数字和特殊字符</span>
            </li>
            <li class="tip-item">
              <DxIcon name="i-mdi:check-circle" class="tip-icon valid" />
              <span>避免使用常见密码</span>
            </li>
          </ul>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <DxButton 
            :loading="loading" 
            size="lg" 
            class="submit-btn" 
            type="submit"
            :disabled="loading"
          >
            <DxIcon name="i-mdi:content-save" class="btn-icon" />
            {{ loading ? '保存中...' : '保存新密码' }}
          </DxButton>
        </div>
      </form>
    </div>

    <!-- 安全提示卡片 -->
    <div class="security-tips-card">
      <div class="tips-card-header">
        <DxIcon name="i-mdi:shield-security" class="security-icon" />
        <h3 class="tips-card-title">安全提示</h3>
      </div>
      <div class="tips-card-content">
        <p class="security-text">
          修改密码后，您需要重新登录以确保账户安全。请妥善保管您的新密码，不要与他人分享。
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.edit-password-container {
  padding: 24px;
  max-width: 600px;
  margin: 0 auto;
  background: var(--g-main-area-bg);
  min-height: 100vh;
}

.password-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
  }
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid hsl(var(--border));

  .header-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--primary) / 0.05) 100%);
    border-radius: 12px;
    border: 1px solid hsl(var(--primary) / 0.2);

    .header-icon {
      font-size: 28px;
      color: hsl(var(--primary));
    }
  }

  .header-content {
    flex: 1;

    .card-title {
      font-size: 24px;
      font-weight: 700;
      color: hsl(var(--foreground));
      margin: 0 0 8px 0;
      line-height: 1.2;
    }

    .card-subtitle {
      font-size: 16px;
      color: hsl(var(--muted-foreground));
      margin: 0;
      line-height: 1.5;
    }
  }
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-item {
  position: relative;
  margin: 0;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: hsl(var(--background));
  border: 2px solid hsl(var(--border));
  border-radius: 12px;
  transition: all 0.2s ease;
  overflow: hidden;

  &:focus-within {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
  }

  &.input-error {
    border-color: hsl(var(--destructive));
    box-shadow: 0 0 0 3px hsl(var(--destructive) / 0.1);
  }

  .input-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: hsl(var(--muted) / 0.3);
    border-right: 1px solid hsl(var(--border));

    .icon {
      font-size: 20px;
      color: hsl(var(--muted-foreground));
    }
  }

  .password-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 14px 16px;
    font-size: 16px;
    color: hsl(var(--foreground));
    outline: none;

    &::placeholder {
      color: hsl(var(--muted-foreground));
    }

    &:focus {
      outline: none;
    }
  }
}

.error-message {
  position: absolute;
  bottom: -20px;
  left: 0;
  font-size: 14px;
  color: hsl(var(--destructive));
  font-weight: 500;
}

.password-tips {
  background: hsl(var(--muted) / 0.3);
  border: 1px solid hsl(var(--border));
  border-radius: 12px;
  padding: 20px;
  margin-top: 8px;

  .tips-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .tips-icon {
      font-size: 18px;
      color: hsl(var(--primary));
    }

    .tips-title {
      font-size: 16px;
      font-weight: 600;
      color: hsl(var(--foreground));
    }
  }

  .tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .tip-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: hsl(var(--muted-foreground));

    .tip-icon {
      font-size: 16px;
      color: hsl(var(--success));
    }
  }
}

.submit-section {
  margin-top: 8px;
}

.submit-btn {
  width: 100%;
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.9) 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px hsl(var(--primary) / 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  .btn-icon {
    font-size: 18px;
  }
}

.security-tips-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);

  .tips-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;

    .security-icon {
      font-size: 20px;
      color: hsl(var(--warning));
    }

    .tips-card-title {
      font-size: 18px;
      font-weight: 600;
      color: hsl(var(--foreground));
      margin: 0;
    }
  }

  .tips-card-content {
    .security-text {
      font-size: 14px;
      color: hsl(var(--muted-foreground));
      line-height: 1.6;
      margin: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .edit-password-container {
    padding: 16px;
  }

  .password-card {
    padding: 24px;
  }

  .card-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 16px;

    .header-icon-wrapper {
      width: 48px;
      height: 48px;

      .header-icon {
        font-size: 24px;
      }
    }

    .header-content {
      .card-title {
        font-size: 20px;
      }

      .card-subtitle {
        font-size: 14px;
      }
    }
  }

  .input-wrapper {
    .input-icon {
      width: 44px;
      height: 44px;

      .icon {
        font-size: 18px;
      }
    }

    .password-input {
      padding: 12px 14px;
      font-size: 16px;
    }
  }

  .submit-btn {
    height: 48px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .edit-password-container {
    padding: 12px;
  }

  .password-card {
    padding: 20px;
  }

  .card-header {
    .header-content {
      .card-title {
        font-size: 18px;
      }

      .card-subtitle {
        font-size: 13px;
      }
    }
  }

  .password-tips {
    padding: 16px;

    .tips-header {
      .tips-title {
        font-size: 15px;
      }
    }

    .tip-item {
      font-size: 13px;
    }
  }
}
</style>
